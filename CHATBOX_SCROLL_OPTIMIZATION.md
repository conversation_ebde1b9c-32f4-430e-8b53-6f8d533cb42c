# ChatBox 自动滚动优化说明

## 问题描述

流式数据传输会导致以下自动滚动问题：

1. **频繁的 DOM 变化**：流式数据会动态添加 contentBlocks，包括工作流节点、思维链、内容等
2. **自动展开行为**：工作流节点和思维链组件会自动展开，导致容器高度频繁变化
3. **滚动被打断**：平滑滚动在快速的 DOM 变化中容易被打断，导致无法滚动到底部
4. **响应延迟**：原有的延迟机制（100ms-300ms）对于快速变化的流式数据响应不够及时

## 优化方案

### 1. 流式状态检测

- 新增 `isStreamingRef` 来跟踪是否正在流式传输
- 通过检测 `messageItems` 中是否有 `status === 'loading'` 的消息来判断流式状态

### 2. 智能平滑滚动策略

- **全部使用平滑滚动**：保持一致的用户体验
- **智能分步滚动**：距离较远时先快速定位再平滑滚动，避免平滑滚动被打断
- **优化延迟时间**：给平滑滚动足够的完成时间

### 3. 优化延迟时间

- **用户滚动检测**：流式时 200ms，普通时 300ms（给平滑滚动更多时间）
- **ResizeObserver**：流式时 80ms，普通时 120ms（平衡响应性和平滑度）
- **流式滚动检查**：100ms 的检查，确保平滑滚动完成后到达底部

### 4. 增强的滚动检查

- 流式传输时使用更小的阈值（5-20px）
- 添加专门的流式滚动定时器 `streamScrollTimeoutRef`
- 在流式传输期间进行更频繁的底部检查

### 5. 状态重置机制

- 提交新消息时重置流式状态和滚动高度记录
- 确保每次新对话都有正确的初始状态

## 核心改进

### 智能平滑滚动函数

```typescript
const scrollToBottom = useCallback(() => {
  // 智能分步滚动：距离较远时先快速定位再平滑滚动
  if (distanceFromBottom <= clientHeight) {
    // 距离较近，直接平滑滚动
    scrollContainer.scrollTo({ behavior: 'smooth', top: scrollHeight });
  } else {
    // 距离较远，先快速滚动到接近底部，再平滑滚动
    const intermediatePosition = scrollHeight - clientHeight * 0.8;
    scrollContainer.scrollTo({ behavior: 'auto', top: intermediatePosition });
    setTimeout(() => {
      scrollContainer.scrollTo({ behavior: 'smooth', top: scrollHeight });
    }, 50);
  }
}, []);
```

### 流式状态检测

```typescript
// 检测是否为流式传输状态
const hasLoadingMessage = messageItems.some((item) => item.status === 'loading');
isStreamingRef.current = hasLoadingMessage;
```

### 优化延迟策略

```typescript
// 流式传输时使用适中的延迟，给平滑滚动足够时间
const delay = isStreamingRef.current ? 80 : 120;
const userScrollDelay = isStreamingRef.current ? 200 : 300;
```

## 预期效果

1. **一致的平滑体验**：全程使用平滑滚动，提供更好的视觉体验
2. **智能分步滚动**：距离较远时先快速定位再平滑滚动，避免滚动被打断
3. **优化的延迟时间**：给平滑滚动足够的完成时间，确保滚动到位
4. **流式数据适配**：针对流式传输优化延迟和检查频率

## 兼容性

- 保持原有的用户滚动检测逻辑
- 向后兼容，不影响现有功能
- 优雅降级，即使在不支持某些特性的环境中也能正常工作
