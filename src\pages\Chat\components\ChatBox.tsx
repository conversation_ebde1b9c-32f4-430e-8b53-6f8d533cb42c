import { UserOutlined } from '@ant-design/icons';
import { Bubble, Prompts } from '@ant-design/x';
import { GetProp, Space, Spin } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { IGetAppParametersResponse } from '../type';
import { MessageSender } from './MessageSender';
import { WelcomePlaceholder } from './WelcomePlaceholder';
// import MessageFooter from './message/footer';
import { RoleType } from '@/enums';
import chat from '@/services/chat';
import { ChatAPI } from '@/services/chat/typeings';
import AiLogo from '@/static/images/aiLogo.png';
import { IMessageFileItem, IMessageItem4Render } from '@/types';
import { isTempId } from '@/utils';
import { createStyles } from 'antd-style';
import MessageContent from './message/content';
import MessageFooter from './message/footer';

export interface ChatboxProps {
  /**
   * 应用参数
   */
  appParameters?: IGetAppParametersResponse;
  /**
   * 消息列表
   */
  messageItems: IMessageItem4Render[];
  /**
   * 是否正在请求
   */
  isRequesting: boolean;
  /**
   * 内容提交事件
   * @param value 问题-文本
   * @param files 问题-文件
   */
  onSubmit: (
    value: string,
    options?: {
      files?: any[];
      inputs?: Record<string, unknown>;
    },
  ) => void;
  /**
   * 取消读取流
   */
  onCancel: () => void;
  /**
   * 反馈执行成功后的回调
   */
  feedbackCallback?: (conversationId: string) => void;
  /**
   * 对话 ID
   */
  conversationId?: string;
}

const useStyle = createStyles(({ css }) => {
  return {
    loadingMessage: css`
      background-image: linear-gradient(90deg, #ff6b23 0%, #af3cb8 31%, #53b6ff 89%);
      background-size: 100% 2px;
      background-repeat: no-repeat;
      background-position: bottom;
    `,
  };
});

// 滚动配置常量
const SCROLL_CONFIG = {
  // 阈值配置
  BOTTOM_THRESHOLD: {
    STREAMING: 5,
    NORMAL: 10,
    USER_SCROLL: 20,
  },
  // 延迟配置
  DELAYS: {
    USER_SCROLL: {
      STREAMING: 200,
      NORMAL: 300,
    },
    RESIZE_OBSERVER: {
      STREAMING: 80,
      NORMAL: 120,
    },
    STREAM_CHECK: 100,
    FALLBACK_CHECK: 300,
    STEP_SCROLL: 50,
  },
  // 距离配置
  DISTANCE: {
    INTERMEDIATE_RATIO: 0.8, // 分步滚动时的中间位置比例
  },
} as const;

/**
 * 滚动工具函数
 */
const scrollUtils = {
  /**
   * 判断滚动容器是否在底部
   */
  isAtBottom: (container: HTMLElement, threshold = 20): boolean => {
    const { scrollTop, scrollHeight, clientHeight } = container;
    return scrollHeight - scrollTop - clientHeight <= threshold;
  },

  /**
   * 获取滚动距离信息
   */
  getScrollInfo: (container: HTMLElement) => {
    const { scrollTop, scrollHeight, clientHeight } = container;
    const maxScrollTop = scrollHeight - clientHeight;
    const distanceFromBottom = maxScrollTop - scrollTop;
    return { scrollTop, scrollHeight, clientHeight, maxScrollTop, distanceFromBottom };
  },

  /**
   * 平滑滚动到指定位置
   */
  smoothScrollTo: (container: HTMLElement, top: number, behavior: ScrollBehavior = 'smooth') => {
    container.scrollTo({ behavior, top });
  },
};

/**
 * 自动滚动管理Hook
 */
const useAutoScroll = (
  messageItems: IMessageItem4Render[],
  scrollContainerRef: React.RefObject<HTMLDivElement>,
) => {
  const [shouldAutoScroll, setShouldAutoScroll] = useState<boolean>(true);

  // Refs for scroll management
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const streamScrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastScrollHeightRef = useRef<number>(0);
  const isStreamingRef = useRef<boolean>(false);

  // 清理定时器的工具函数
  const clearTimeouts = useCallback(() => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
      scrollTimeoutRef.current = null;
    }
    if (streamScrollTimeoutRef.current) {
      clearTimeout(streamScrollTimeoutRef.current);
      streamScrollTimeoutRef.current = null;
    }
  }, []);

  // 智能平滑滚动到底部
  const scrollToBottom = useCallback(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const { scrollHeight, clientHeight, distanceFromBottom } =
      scrollUtils.getScrollInfo(scrollContainer);

    // 如果距离很近（小于一个屏幕高度），直接平滑滚动
    // 如果距离较远，分步滚动以避免平滑滚动被打断
    if (distanceFromBottom <= clientHeight) {
      // 距离较近，直接平滑滚动
      scrollUtils.smoothScrollTo(scrollContainer, scrollHeight);
    } else {
      // 距离较远，先快速滚动到接近底部，再平滑滚动
      const intermediatePosition =
        scrollHeight - clientHeight * SCROLL_CONFIG.DISTANCE.INTERMEDIATE_RATIO;
      scrollUtils.smoothScrollTo(scrollContainer, intermediatePosition, 'auto');

      // 短暂延迟后再平滑滚动到底部
      setTimeout(() => {
        scrollUtils.smoothScrollTo(scrollContainer, scrollHeight);
      }, SCROLL_CONFIG.DELAYS.STEP_SCROLL);
    }
  }, [scrollContainerRef]);

  // 重置滚动状态
  const resetScrollState = useCallback(() => {
    setShouldAutoScroll(true);
    isStreamingRef.current = false;
    lastScrollHeightRef.current = 0;
    clearTimeouts();
  }, [clearTimeouts]);

  return {
    shouldAutoScroll,
    setShouldAutoScroll,
    scrollToBottom,
    resetScrollState,
    clearTimeouts,
    // 内部状态refs，供其他effect使用
    scrollTimeoutRef,
    streamScrollTimeoutRef,
    lastScrollHeightRef,
    isStreamingRef,
  };
};

/**
 * 对话内容区
 */
export const Chatbox = (props: ChatboxProps) => {
  const {
    messageItems,
    isRequesting,
    onSubmit,
    onCancel,
    conversationId,
    appParameters,
    feedbackCallback,
  } = props;
  const [content, setContent] = useState('');
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 使用自动滚动Hook
  const {
    shouldAutoScroll,
    setShouldAutoScroll,
    scrollToBottom,
    resetScrollState,
    clearTimeouts,
    scrollTimeoutRef,
    streamScrollTimeoutRef,
    lastScrollHeightRef,
    isStreamingRef,
  } = useAutoScroll(messageItems, scrollContainerRef);

  const { styles } = useStyle();

  const roles: GetProp<typeof Bubble.List, 'roles'> = {
    ai: {
      placement: 'start',
      avatar: {
        src: <img src={AiLogo} alt="AI Logo" />,
        style: { background: '#ffffff', border: '1px solid #F0F0F0' },
      },
      style: {
        // 减去一个头像的宽度
        maxWidth: 'calc(100% - 44px)',
      },
      loadingRender: () => (
        <Space className="my-[8px]">
          <Spin size="small" />
          loading...
        </Space>
      ),
    },
    user: {
      placement: 'end',
      avatar: {
        icon: <UserOutlined />,
        style: {
          background: '#87d068',
        },
      },
      style: {
        // 减去一个头像的宽度
        maxWidth: 'calc(100% - 44px)',
        marginLeft: '44px',
      },
    },
  };

  const items: GetProp<typeof Bubble.List, 'items'> = useMemo(() => {
    return messageItems?.map((messageItem) => {
      return {
        key: `${messageItem.id}-${messageItem.role}`,
        classNames: {
          content: messageItem.status === 'loading' ? styles.loadingMessage : '',
        },
        // 不要开启 loading 和 typing, 否则流式会无效
        loading: messageItem.requestStatus === 'loading',
        typing: messageItem.status === 'loading' ? { step: 5, interval: 20, suffix: '...' } : false,
        messageRender: () => {
          return <MessageContent onSubmit={onSubmit} messageItem={messageItem} />;
        },
        // 用户发送消息时，status 为 local，需要展示为用户头像
        role: messageItem.role === RoleType.local ? RoleType.user : messageItem.role,
        footer: messageItem.status !== 'loading' && (
          <div className="flex items-center">
            <MessageFooter
              feedbackApi={(params) => {
                console.log('feedbackApi:', params);
                const { messageId, rating, content } = params;
                return chat.api.comment({
                  intention_id: Number(messageId),
                  is_good: rating,
                  user_comment: content,
                });
              }}
              messageId={messageItem.id}
              messageContent={messageItem.content}
              role={messageItem.role}
              feedback={{
                rating: messageItem.feedback?.rating,
                callback: () => {
                  feedbackCallback?.(conversationId!);
                },
              }}
            />
            {messageItem.role === RoleType.ai && messageItem.created_at && (
              <div className="ml-3 text-sm text-desc">回复时间：{messageItem.created_at}</div>
            )}
          </div>
        ),
      };
    }) as GetProp<typeof Bubble.List, 'items'>;
  }, [messageItems, conversationId, onSubmit]);

  const onPromptsItemClick: GetProp<typeof Prompts, 'onItemClick'> = (info) => {
    setContent((info.data as ChatAPI.AgentList).prompt_template);
  };

  const handleSubmit = (
    content: string,
    options?: {
      files?: IMessageFileItem[];
    },
  ) => {
    if (!content) return;

    // 提交消息时重置滚动状态和流式状态
    onSubmit(content, options);
    setContent('');
    resetScrollState();
  };

  // 用户滚动检测：区分流式传输和普通状态
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const handleUserScroll = () => {
      // 清除之前的定时器
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // 流式传输时使用适中的延迟，平衡响应性和平滑滚动
      const delay = isStreamingRef.current
        ? SCROLL_CONFIG.DELAYS.USER_SCROLL.STREAMING
        : SCROLL_CONFIG.DELAYS.USER_SCROLL.NORMAL;

      // 延迟检查，让滚动完成
      scrollTimeoutRef.current = setTimeout(() => {
        const threshold = isStreamingRef.current
          ? SCROLL_CONFIG.BOTTOM_THRESHOLD.USER_SCROLL
          : SCROLL_CONFIG.BOTTOM_THRESHOLD.NORMAL;
        const isAtBottom = scrollUtils.isAtBottom(scrollContainer, threshold);
        setShouldAutoScroll(isAtBottom);
      }, delay);
    };

    // 只监听明确的用户滚动操作
    scrollContainer.addEventListener('wheel', handleUserScroll, { passive: true });
    scrollContainer.addEventListener('touchstart', handleUserScroll, { passive: true });

    return () => {
      scrollContainer.removeEventListener('wheel', handleUserScroll);
      scrollContainer.removeEventListener('touchstart', handleUserScroll);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // 监听消息变化，在应该自动滚动时执行滚动
  useEffect(() => {
    if (!shouldAutoScroll) return;

    // 检测是否为流式传输状态
    const hasLoadingMessage = messageItems.some((item) => item.status === 'loading');
    isStreamingRef.current = hasLoadingMessage;

    // 清除之前的流式滚动定时器
    if (streamScrollTimeoutRef.current) {
      clearTimeout(streamScrollTimeoutRef.current);
    }

    // 使用 requestAnimationFrame 确保 DOM 更新完成后再滚动
    requestAnimationFrame(() => {
      const scrollContainer = scrollContainerRef.current;
      if (!scrollContainer) return;

      const currentHeight = scrollContainer.scrollHeight;
      lastScrollHeightRef.current = currentHeight;

      // 流式传输时使用更频繁的滚动
      if (isStreamingRef.current) {
        scrollToBottom(); // 平滑滚动

        // 流式传输时设置更短的延迟检查，确保平滑滚动完成后到达底部
        streamScrollTimeoutRef.current = setTimeout(() => {
          if (scrollContainer && shouldAutoScroll && isStreamingRef.current) {
            const isAtBottom = scrollUtils.isAtBottom(
              scrollContainer,
              SCROLL_CONFIG.BOTTOM_THRESHOLD.STREAMING,
            );
            if (!isAtBottom) {
              // 如果平滑滚动没有完全到达底部，再次尝试平滑滚动
              scrollUtils.smoothScrollTo(scrollContainer, scrollContainer.scrollHeight);
            }
          }
        }, SCROLL_CONFIG.DELAYS.STREAM_CHECK);
      } else {
        // 非流式传输时使用平滑滚动
        scrollToBottom();

        // 额外保障：再次检查是否真的到底了
        setTimeout(() => {
          if (scrollContainer && shouldAutoScroll) {
            const isAtBottom = scrollUtils.isAtBottom(
              scrollContainer,
              SCROLL_CONFIG.BOTTOM_THRESHOLD.NORMAL,
            );
            if (!isAtBottom) {
              // 使用平滑滚动而不是强制滚动
              scrollUtils.smoothScrollTo(scrollContainer, scrollContainer.scrollHeight);
            }
          }
        }, SCROLL_CONFIG.DELAYS.FALLBACK_CHECK);
      }
    });
  }, [items, shouldAutoScroll, scrollToBottom, messageItems]);

  // 监听内容高度变化，保持滚动条在底部
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    let resizeTimer: NodeJS.Timeout | null = null;
    let lastHeight = scrollContainer.scrollHeight;

    const resizeObserver = new ResizeObserver(() => {
      if (resizeTimer) clearTimeout(resizeTimer);

      // 流式传输时使用适中的延迟，给平滑滚动足够时间
      const delay = isStreamingRef.current
        ? SCROLL_CONFIG.DELAYS.RESIZE_OBSERVER.STREAMING
        : SCROLL_CONFIG.DELAYS.RESIZE_OBSERVER.NORMAL;

      resizeTimer = setTimeout(() => {
        const currentHeight = scrollContainer.scrollHeight;

        // 只有高度真正发生变化且应该自动滚动时才执行
        if (currentHeight !== lastHeight && shouldAutoScroll) {
          lastHeight = currentHeight;

          // 始终使用平滑滚动
          scrollToBottom();
        }
      }, delay);
    });

    resizeObserver.observe(scrollContainer);

    return () => {
      resizeObserver.disconnect();
      if (resizeTimer) clearTimeout(resizeTimer);
    };
  }, [shouldAutoScroll, scrollToBottom]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return clearTimeouts;
  }, [clearTimeouts]);

  return (
    <div className="w-full h-full my-0 mx-auto box-border flex flex-col gap-4 bg-white">
      <div className="flex-1 overflow-auto" ref={scrollContainerRef}>
        {/* 欢迎占位 */}
        {!items?.length && isTempId(conversationId) ? (
          <div className="m-auto max-w-[1000px]">
            <WelcomePlaceholder
              appParameters={appParameters}
              onPromptItemClick={(info) => {
                onPromptsItemClick(info);
                setShouldAutoScroll(true);
              }}
            />
          </div>
        ) : (
          <Bubble.List
            className="px-[calc(50%_-_500px)]"
            autoScroll={false}
            items={items}
            roles={roles}
          />
        )}
      </div>
      <div className="bg-white w-full max-w-[1000px] m-auto">
        {/* 输入框 */}
        <MessageSender
          className="w-full px-3"
          content={content}
          setContent={setContent}
          onChange={(value) => setContent(value)}
          onSubmit={handleSubmit}
          isRequesting={isRequesting}
          uploadFileApi={(params, options) => {
            console.log('uploadFileApi:', params);
            return chat.api.uploadFile(params, options);
          }}
          onCancel={onCancel}
          conversationId={conversationId}
        />
        <div className="text-gray-400 text-sm text-center h-8 leading-8">
          内容由 AI 生成, 仅供参考
        </div>
      </div>
    </div>
  );
};
